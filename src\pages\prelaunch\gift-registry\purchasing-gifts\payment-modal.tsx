import { Button } from '../../../../components/button/onboardingButton';

interface PaymentModalProps {
  onPayNow: () => void;
  onPayLater: () => void;
  onClose: () => void;
}

export const PaymentModal = ({ onPayNow, onPayLater, onClose }: PaymentModalProps) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center">
        {/* Money Illustration */}
        <div className="mb-6">
          <div className="w-24 h-24 mx-auto mb-4 relative">
            {/* Money Stack Illustration */}
            <svg
              width="96"
              height="96"
              viewBox="0 0 96 96"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="w-full h-full"
            >
              {/* Green Bills */}
              <rect x="20" y="30" width="40" height="25" rx="4" fill="#22C55E" />
              <rect x="25" y="25" width="40" height="25" rx="4" fill="#16A34A" />
              <rect x="30" y="20" width="40" height="25" rx="4" fill="#15803D" />
              
              {/* Red Bill */}
              <rect x="35" y="15" width="40" height="25" rx="4" fill="#EF4444" />
              
              {/* Dollar Sign on Red Bill */}
              <text x="55" y="32" fill="white" fontSize="16" fontWeight="bold">$</text>
              
              {/* Coins */}
              <circle cx="25" cy="65" r="8" fill="#F59E0B" />
              <circle cx="40" cy="70" r="6" fill="#D97706" />
              <circle cx="60" cy="68" r="7" fill="#F59E0B" />
              <circle cx="75" cy="60" r="5" fill="#D97706" />
              <circle cx="70" cy="75" r="6" fill="#F59E0B" />
              
              {/* Dollar signs on coins */}
              <text x="22" y="69" fill="white" fontSize="8" fontWeight="bold">$</text>
              <text x="37" y="74" fill="white" fontSize="6" fontWeight="bold">$</text>
              <text x="57" y="72" fill="white" fontSize="7" fontWeight="bold">$</text>
            </svg>
          </div>
        </div>

        {/* Modal Title */}
        <h3 className="text-xl font-semibold text-grey-750 mb-4">
          Continue to Payment?
        </h3>

        {/* Modal Description */}
        <p className="text-sm text-grey-500 mb-8 leading-relaxed">
          Please note that payment for a gift reservation has to be made within 7 days after which the reservation would be cancelled, would you like to make payment now?
        </p>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            variant="primary"
            size="md"
            className="text-white bg-primary-650 w-full"
            onClick={onPayNow}
          >
            Yes pay now
          </Button>
          
          <Button
            variant="secondary"
            size="md"
            className="text-grey-600 bg-grey-100 w-full border-0"
            onClick={onPayLater}
          >
            No, pay later
          </Button>
        </div>
      </div>
    </div>
  );
};
