import { useState } from "react";
import stackMoney from "../../../../assets/images/stack-money2.svg";

interface PaymentModalProps {
  onPayNow: () => void;
  onPayLater: () => void;
  onClose: () => void;
}

export const PaymentModal = ({
  onPayNow,
  onPayLater,
  onClose,
}: PaymentModalProps) => {
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);

  if (showPaymentMethods) {
    return (
      <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
        <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 hover:text-grey-50 transition-colors cursor-pointer"
          >
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.4"
                d="M20.0026 36.6673C29.2074 36.6673 36.6693 29.2054 36.6693 20.0007C36.6693 10.7959 29.2074 3.33398 20.0026 3.33398C10.7979 3.33398 3.33594 10.7959 3.33594 20.0007C3.33594 29.2054 10.7979 36.6673 20.0026 36.6673Z"
                fill="#4D55F2"
              />
              <path
                d="M21.7682 19.9996L25.6016 16.1663C26.0849 15.6829 26.0849 14.8829 25.6016 14.3996C25.1182 13.9163 24.3182 13.9163 23.8349 14.3996L20.0016 18.2329L16.1682 14.3996C15.6849 13.9163 14.8849 13.9163 14.4016 14.3996C13.9182 14.8829 13.9182 15.6829 14.4016 16.1663L18.2349 19.9996L14.4016 23.8329C13.9182 24.3163 13.9182 25.1163 14.4016 25.5996C14.6516 25.8496 14.9682 25.9663 15.2849 25.9663C15.6016 25.9663 15.9182 25.8496 16.1682 25.5996L20.0016 21.7663L23.8349 25.5996C24.0849 25.8496 24.4016 25.9663 24.7182 25.9663C25.0349 25.9663 25.3516 25.8496 25.6016 25.5996C26.0849 25.1163 26.0849 24.3163 25.6016 23.8329L21.7682 19.9996Z"
                fill="#4D55F2"
              />
            </svg>
          </button>

          {/* Payment Methods Content */}
          <div className="p-8 pt-16">
            <h2 className="text-[28px] font-semibold text-grey-50 mb-2 text-center">
              Select Payment Method
            </h2>
            <p className="text-base text-grey-250 mb-8 text-center">
              Please choose a payment option below
            </p>

            {/* Payment Options */}
            <div className="space-y-4">
              {/* Direct Debit */}
              <div
                onClick={onPayNow}
                className="border border-grey-200 rounded-2xl p-4 hover:border-primary-300 cursor-pointer transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 8.5H22"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6 16.5H8"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10.5 16.5H14.5"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6.44 3.5H17.55C21.11 3.5 22 4.39 22 7.95V16.04C22 19.6 21.11 20.49 17.56 20.49H6.44C2.89 20.49 2 19.6 2 16.05V7.95C2 4.39 2.89 3.5 6.44 3.5Z"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      DIRECT DEBIT
                    </h3>
                    <p className="text-sm text-grey-500">
                      Automatically withdraws funds from your account
                    </p>
                  </div>
                </div>
              </div>

              {/* Bank Transfer */}
              <div
                onClick={onPayNow}
                className="border border-grey-200 rounded-2xl p-4 hover:border-primary-300 cursor-pointer transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 2L2 7V10H22V7L12 2Z"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2 10V19C2 19.5523 2.44772 20 3 20H21C21.5523 20 22 19.5523 22 19V10"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6 14V16"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10 14V16"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M14 14V16"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M18 14V16"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      BANK TRANSFER
                    </h3>
                    <p className="text-sm text-grey-500">
                      Manually send money from your bank account
                    </p>
                  </div>
                </div>
              </div>

              {/* Debit Card */}
              <div
                onClick={onPayNow}
                className="border border-grey-200 rounded-2xl p-4 hover:border-primary-300 cursor-pointer transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 8.5H22"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6 16.5H8"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M10.5 16.5H14.5"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeMiterlimit="10"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M6.44 3.5H17.55C21.11 3.5 22 4.39 22 7.95V16.04C22 19.6 21.11 20.49 17.56 20.49H6.44C2.89 20.49 2 19.6 2 16.05V7.95C2 4.39 2.89 3.5 6.44 3.5Z"
                        stroke="#4D55F2"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <circle cx="18" cy="6" r="2" fill="#4D55F2" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-grey-750 text-lg">
                      DEBIT CARD
                    </h3>
                    <p className="text-sm text-grey-500">
                      Pay instantly using your credit card details
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-[#00002666]/40 flex items-center justify-center z-50 font-rethink">
      <div className="bg-white rounded-3xl w-full md:w-[522px] mx-4 relative">
        {/* Money Illustration Background */}
        <div className="flex justify-center rounded-t-3xl mb-6 p-8 pt-[60px] bg-[#F5F6FE] h-[280px]">
          <div className="relative">
            <img src={stackMoney} alt="" className="w-[220px] h-auto" />
          </div>
        </div>

        {/* Modal Content */}
        <div className="px-8 pb-8 text-center">
          <h2 className="text-[28px] font-semibold text-grey-50 mb-4">
            Continue to Payment?
          </h2>

          <p className="text-base text-grey-250 mb-8 leading-relaxed max-w-[400px] mx-auto">
            Please note that payment for a gift reservation has to be made
            within 7 days after which the reservation would be cancelled, would
            you like to make payment now?
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => setShowPaymentMethods(true)}
              className="w-full h-[48px] bg-primary text-white rounded-full font-semibold text-base hover:bg-primary/90 transition-all"
            >
              Yes pay now
            </button>

            <button
              onClick={onPayLater}
              className="w-full h-[48px] bg-grey-100 text-grey-600 rounded-full font-semibold text-base hover:bg-grey-200 transition-all"
            >
              No, pay later
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
