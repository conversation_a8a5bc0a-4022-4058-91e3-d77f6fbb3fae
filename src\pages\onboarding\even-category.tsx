import { useState } from 'react';
import { Button } from '../../components/button/onboardingButton';
import { ArrowCircleRight2 } from 'iconsax-react';
import { motion } from 'framer-motion';

interface EventCategoryProps {
  onNext: (categoryId: number) => void;
  initialData?: number;
}

export const EventCategory = ({ onNext, initialData }: EventCategoryProps) => {
  const [selectedCategory, setSelectedCategory] = useState<number | null>(initialData || null);

  const categories = [
    { id: 1, name: '🎉 Birthdays' },
    { id: 2, name: '💍 Weddings' },
    { id: 3, name: '💼 Corporate Event' },
    { id: 4, name: '🏆 Anniversary' },
    { id: 5, name: '💸 Fundraisers' },
    { id: 6, name: '🚀 Product Launch' },
  ];

  const handleCategorySelect = (id: number) => {
    setSelectedCategory(selectedCategory === id ? null : id);
  };

  const handleContinue = () => {
    if (selectedCategory) {
      onNext(selectedCategory);
    }
  };

  const textVariants = {
    hidden: { 
      opacity: 0.3,  // More transparent initially
      x: 2,
      scale: 0.99
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        staggerChildren: 0.04
      }
    }
  };

  const letterVariants = {
    hidden: {
      opacity: 0.2,  // More transparent initially
      x: 2,
      skewX: 2
    },
    visible: {
      opacity: 1,
      x: 0,
      skewX: 0,
      transition: {
        duration: 0.25,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    }
  };

  const cardVariants = {
    hidden: {
      x: '-20vw', 
      rotate: -4, 
      opacity: 0,
    },
    visible: {
      x: 0,
      y: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,
        damping: 25,
        mass: 0.2,
        delay: 0.2, // Slight delay after text animation
      },
    },
  };

  return (
    <div>
      <motion.h2 
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        variants={textVariants}
      >
        {["What", "kind", "of", "event", "are"].map((word, i) => (
          <motion.span
            key={i}
            variants={letterVariants}
            style={{ 
              display: 'inline-block', 
              marginRight: '8px',
              transformOrigin: 'left center'
            }}
          >
            {word}
          </motion.span>
        ))}
        <br />
        {["you", "planning?"].map((word, i) => (
          <motion.span
            key={i}
            variants={letterVariants}
            style={{ 
              display: 'inline-block', 
              marginRight: '8px',
              transformOrigin: 'left center'
            }}
          >
            {word}
          </motion.span>
        ))}
      </motion.h2>
      <motion.div 
        className="bg-white py-6 px-5 rounded-[20px]"
        initial="hidden"
        animate="visible"
        variants={cardVariants}
      >
        <div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                className={`flex items-center justify-center px-4 py-2 cursor-pointer rounded-full border border-grey-900 ${
                  selectedCategory === category.id
                    ? 'bg-primary-250 border-primary-300 italic font-bold text-dark-blue-200'
                    : 'text-grey font-medium'
                }`}
                onClick={() => handleCategorySelect(category.id)}>
                <span
                  className={`text-sm text-grey font-medium ${
                    selectedCategory !== null && selectedCategory !== category.id
                      ? 'blur-xs'
                      : 'blur-none'
                  }`}>
                  {category.name}
                </span>
              </button>
            ))}
          </div>

          <Button
            variant="primary"
            size="md"
            className={`text-white mt-19 ${
              selectedCategory ? 'bg-primary-650' : '!bg-primary-650/35'
            }`}
            onClick={handleContinue}
            iconRight={
              <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
            }>
            Continue
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
