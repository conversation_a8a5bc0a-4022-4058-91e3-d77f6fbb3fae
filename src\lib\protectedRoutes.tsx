import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect } from 'react';

export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  if (!token) {
    return null;
  }
  
  return children;
};

export const AuthRoute = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();

  return token ? <Navigate to="/" replace /> : <Outlet />;
};
