import { useState } from 'react';
import { FormInput } from '../../../../components/inputs/form-input/form-input';
import { ArrowCircleRight2, ArrowDown2 } from 'iconsax-react';
import { Button } from '../../../../components/button/onboardingButton';

export const GifterDetails = ({ onContinue }: { onContinue?: () => void }) => {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  return (
    <div className="pl-5 pr-7">
      <p className="text-base my-4.5">
        Let's the recipient know who's gifting 🥳
      </p>
      <div>
        <FormInput
          label="Full Name"
          placeholder="Enter your full name"
          type="name"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          text="This would be recorded and sent to the recipient"
        />
        <FormInput
          label="Email Address"
          placeholder="Enter your email address"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <FormInput
          label="Mobile Number"
          placeholder="Enter mobile number"
          type="tel"
          value={mobileNumber}
          onChange={(e) => setMobileNumber(e.target.value)}
          leftAddon={
            <div className="flex items-center">
              <span className="text-grey-500 font-semibold mr-1 italic text-base">
                +234
              </span>
              <ArrowDown2 size={16} color="#717680" />
            </div>
          }
        />
        <Button
          variant="primary"
          size="md"
          className={`text-white bg-primary-650 mb-19 `}
          iconRight={
            <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
          }
          onClick={onContinue}>
          Continue
        </Button>
      </div>
    </div>
  );
};
