import { Outlet, useNavigate } from 'react-router-dom';
import { Header } from '../../../components/dashboard/header';
import { Footer } from '../footer';
import { useEffect } from 'react';

export const PrelaunchDashboard = () => {
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.userAppToken;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  if (!token) {
    return null;
  }
  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[561px] mx-auto">
        <Header />
        <Outlet />
      </div>
      <Footer />
    </div>
  );
};
