import { useState, ChangeEvent, useEffect, useRef } from 'react';
import { Button } from '../../components/button/onboardingButton';
import {
  ArrowCircleRight2,
  Calendar,
  Clock,
  Gallery,
  Location,
} from 'iconsax-react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  fadeInRightTextVariants,
  fadeInRightLetterVariants,
} from '../../components/reuseables/animations/animations';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { format } from 'date-fns';

interface EventDetailsFormProps {
  onNext: (details: {
    name: string;
    date: string;
    time: string;
    location: string;
    image?: File;
    imageUrl?: string;
  }) => void;
  initialData?: {
    name: string;
    date: string;
    time: string;
    location: string;
    imageUrl?: string;
    imageFile?: File;
  };
  direction: 'forward' | 'backward';
}

export const EventDetailsForm = ({
  onNext,
  initialData,
  direction,
}: EventDetailsFormProps) => {
  const [eventTitle, setEventTitle] = useState(initialData?.name || '');
  const [eventDate, setEventDate] = useState(initialData?.date || '');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    initialData?.date ? new Date(initialData.date) : undefined
  );
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [eventTime, setEventTime] = useState(initialData?.time || '');
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [eventLocation, setEventLocation] = useState(
    initialData?.location || ''
  );
  const [imageFile, setImageFile] = useState<File | null>(
    initialData?.imageFile || null
  );
  const [imagePreview, setImagePreview] = useState<string | null>(
    initialData?.imageUrl || null
  );
  
  const datePickerRef = useRef<HTMLDivElement>(null);
  const timePickerRef = useRef<HTMLDivElement>(null);

  // Generate time options in 24-hour format (30 min intervals)
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });

  // Close date picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
      if (timePickerRef.current && !timePickerRef.current.contains(event.target as Node)) {
        setShowTimePicker(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  useEffect(() => {
    return () => {
      if (imagePreview && !initialData?.imageUrl) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview, initialData?.imageUrl]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      setEventDate(format(date, 'MMMM dd, yyyy'));
      setShowDatePicker(false);
    }
  };

  const handleTimeSelect = (time: string) => {
    setEventTime(time);
    setShowTimePicker(false);
  };

  const validateForm = () => {
    return (
      eventTitle.trim() !== '' &&
      eventDate.trim() !== '' &&
      eventTime.trim() !== '' &&
      eventLocation.trim() !== ''
    );
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onNext({
        name: eventTitle,
        date: eventDate,
        time: eventTime,
        location: eventLocation,
        image: imageFile || undefined,
        imageUrl: imagePreview || undefined,
      });
    }
  };

  // Create direction-based variants
  const getCardVariants = (direction: 'forward' | 'backward') => ({
    hidden: {
      x: direction === 'forward' ? '20vw' : '-20vw',
      rotate: direction === 'forward' ? 4 : -4,
      opacity: 0,
    },
    visible: {
      x: 0,
      rotate: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 180,    
        damping: 20,       
        mass: 0.2,         
        delay: 0.2,
        velocity: 2        
      },
    },
  });

  // CSS for the date picker
  const datePickerStyles = {
    button: { color: '#4D55F2' },
    caption: { color: '#4D55F2' },
    day_selected: { backgroundColor: '#4D55F2' },
    day_today: { color: '#4D55F2', fontWeight: 'bold' }
  };

  // Disable past dates
  const disabledDays = { before: new Date() };

  return (
    <div>
      <motion.h2
        className="text-xl md:text-[40px] font-medium leading-[114.99999999999999%] mb-10"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={fadeInRightTextVariants}>
        <AnimatePresence mode="wait">
          {["Let's", 'curate', 'your'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}
              className={
                word === 'curate'
                  ? 'bg-gradient-to-b from-[#343CD8] via-[#A6AAF9] to-[#A6AAF9] bg-clip-text text-transparent italic'
                  : ''
              }>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
          <br />
          {['first', 'event', '🎉'].map((word, i) => (
            <motion.span
              key={i}
              variants={fadeInRightLetterVariants}
              style={{
                display: 'inline-block',
                marginRight: '8px',
                transformOrigin: 'left center',
                position: 'relative',
              }}>
              {word}
              <motion.span
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  opacity: 0,
                }}
                initial={{ opacity: 0, x: -4 }}
                animate={{ opacity: 0 }}
                exit={{ opacity: 0.2, x: 4 }}>
                {word}
              </motion.span>
            </motion.span>
          ))}
        </AnimatePresence>
      </motion.h2>
      <motion.div
        className="bg-white rounded-[20px] mt-14 px-5 py-6 w-full"
        initial="hidden"
        animate="visible"
        variants={getCardVariants(direction)}>
        {/* Image circle */}
        <div className="w-26 h-26 -translate-y-15 border-[9px] border-white rounded-full bg-cus-pink-900 flex items-center justify-center cursor-pointer relative overflow-hidden">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="absolute inset-0 opacity-0 cursor-pointer z-10"
          />
          {imagePreview ? (
            <img
              src={imagePreview}
              alt="Event"
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <Gallery variant="Bulk" size="62" color="#992600" />
          )}
        </div>

        <div className="space-y-4 -mt-10">
          {/* Event Title */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Event Title
            </label>
            <input
              type="text"
              placeholder="Give your event a title"
              value={eventTitle}
              onChange={(e) => setEventTitle(e.target.value)}
              className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base "
            />
          </div>

          {/* Event Date and Time row */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Event Date
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Select a Date"
                  value={eventDate}
                  readOnly
                  onClick={() => setShowDatePicker(true)}
                  className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div 
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowDatePicker(true)}
                >
                  <Calendar variant="Bulk" size="20" color="#292D32" />
                </div>
                
                {/* Date Picker Modal */}
                {showDatePicker && (
                  <div 
                    ref={datePickerRef}
                    className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-4"
                  >
                    <style>
                      {`
                        .rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
                          background-color: #4D55F2;
                        }
                        .rdp-day_today {
                          font-weight: bold;
                          color: #4D55F2;
                        }
                        .rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
                          background-color: rgba(77, 85, 242, 0.1);
                        }
                        .rdp-caption_label {
                          color: #4D55F2;
                          font-weight: 600;
                        }
                      `}
                    </style>
                    <DayPicker
                      mode="single"
                      selected={selectedDate}
                      onSelect={handleDateSelect}
                      disabled={disabledDays}
                      styles={datePickerStyles}
                      fromMonth={new Date()}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="max-w-[118px]">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Time of Event
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="00:00"
                  value={eventTime}
                  readOnly
                  onClick={() => setShowTimePicker(true)}
                  className="w-full py-2.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div 
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowTimePicker(true)}
                >
                  <Clock variant="Bulk" size="20" color="#292D32" />
                </div>
                
                {/* Time Picker Dropdown */}
                {showTimePicker && (
                  <div 
                    ref={timePickerRef}
                    className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-2 right-0 w-[120px] max-h-[200px] overflow-y-auto"
                    style={{ scrollbarWidth: 'thin' }}
                  >
                    <div className="flex flex-col">
                      {timeOptions.map((time) => (
                        <button
                          key={time}
                          onClick={() => handleTimeSelect(time)}
                          className={`text-left px-3 py-2 hover:bg-primary-250 rounded-md text-sm ${
                            eventTime === time ? 'bg-primary-250 text-primary-650 font-medium' : 'text-grey-500'
                          }`}
                        >
                          {time}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Location */}
          <div>
            <label className="block text-grey-500 text-sm font-medium mb-2">
              Location
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Type your location"
                value={eventLocation}
                onChange={(e) => setEventLocation(e.target.value)}
                className="w-full py-2.5 px-3.5 pl-10 rounded-full  border border-grey-200 placeholder:text-grey-300 outline-none text-base"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <Location variant="Bulk" size="20" color="#292D32" />
              </div>
            </div>
          </div>

          <Button
            variant="primary"
            size="md"
            className={`text-white mt-14 ${
              validateForm() ? 'bg-primary-650' : 'bg-primary-650/35'
            }`}
            onClick={handleSubmit}
            iconRight={
              <ArrowCircleRight2 size="20" color="#fff" variant="Bulk" />
            }>
            Continue
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
