import gift from '../../../../assets/images/gi-ft.png'
export const SuccessPayment = () => {
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="relative w-full pt-20 md:pt-34 px-4 md:px-0 pb-20">
          <div
            className="absolute inset-0 h-[720px] md:h-[669px] top-0 bg-[url(/src/assets/animations/gift.gif)] opacity-40 z-0"
            style={{ backgroundSize: 'cover' }}
          />
          <div className="relative z-20 bg-white border-t border-white rounded-[20px] text-center max-w-[450px] w-full mx-auto shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            <div
              className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[262px] w-full overflow-hidden"
              style={{
                clipPath:
                  'polygon(0 0, 100% 0, 100% 100%, 70% 95%, 30% 95%, 0 100%)',
              }}
            />

            <div className="absolute top-10 sm:-top-5 left-0 overflow-hidden">
              <img
                src={gift}
                alt="Wedding celebration"
                className="w-full h-full object-cover"
              />
            </div>

            <div className="flex flex-col items-center text-center py-12 px-4 w-full">
              <h2 className="text-4xl font-medium my-2 text-dark-blue">
                You Just gifted <br />{' '}
                <span className="text-[26px] text-grey-250">
                  Olatunde successfully{' '}
                </span>
              </h2>
              <p className="text-grey-250 text-base mt-4 mb-7.5">
                You just gifted Olatunde an <span className='italic text-primary underline font-bold'>Iphone 15 Pro</span>
              </p>
              <button
                type="button"
                // onClick={guestlist}
                className="bg-primary-650 cursor-pointer text-base max-w-[306px] w-full text-white  py-3 px-6 font-semibold rounded-full hover:bg-[#4A48E0] transition-colors">
                <span>Back to Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
