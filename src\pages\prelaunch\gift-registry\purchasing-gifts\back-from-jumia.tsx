import { CloseCircle, Tag2 } from 'iconsax-react';
import { useState } from 'react';
import phone from '../../../../assets/images/phone.png';
import { PurchaseTrue } from './purchase-true';
export const BackFromJumia = () => {
  const [purchased, setPurchased] = useState(false);

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)] flex items-center justify-center">
        <div className="bg-white mt-16 rounded-2xl  w-full max-w-md relative ">
          <button className="absolute top-4 right-4 w-8 h-8  flex items-center justify-center">
            <CloseCircle size="33" color="#634C42" variant="Bulk" />
          </button>
          <img
            src={phone}
            alt="phone"
            className="w-full h-[300px] rounded-t-2xl"
          />
          <div className="text-center mb-6 mt-5">
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              Iphone 15 Pro
            </h2>
            <p className="text-sm text-gray-600 mb-3">Apple's IOS iphone 15</p>
            <div className="mt-3  flex mx-auto items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
              <Tag2 size={12} variant="Bulk" color="#5856D6 " />
              <span className="text-perple-50 text-sm font-bold">
                ₦1,650,000
              </span>
            </div>{' '}
          </div>

          <div className="text-center mb-6">
            <p className="text-gray-800 font-medium">
              Have you purchased this gift item?
            </p>
          </div>

          <div className="flex gap-4 justify-center mb-10">
            <button
              onClick={() => setPurchased(true)}
              className={`px-6 py-2 rounded-full font-medium transition-all bg-primary text-white`}>
              Yes, I have
            </button>
            <button
              onClick={() => setPurchased(false)}
              className={`px-6 py-2 rounded-full font-medium transition-all bg-cus-pink-500`}>
              No, I haven't
            </button>
          </div>
        </div>
      </div>
      {purchased === true && <PurchaseTrue/>}
    </div>
  );
};
